// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "BodyStateDevice.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef BODYSTATE_BodyStateDevice_generated_h
#error "BodyStateDevice.generated.h already included, missing '#pragma once' in BodyStateDevice.h"
#endif
#define BODYSTATE_BodyStateDevice_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateDevice_h_31_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBodyStateDevice_Statics; \
	static class UScriptStruct* StaticStruct();


template<> BODYSTATE_API UScriptStruct* StaticStruct<struct FBodyStateDevice>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateDevice_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
