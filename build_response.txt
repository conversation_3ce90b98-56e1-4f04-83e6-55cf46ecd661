Build started at 11:31 AM...
1>------ Build started: Project: IlPalazzo, Configuration: Development_Editor x64 ------
1>Using bundled DotNet SDK version: 8.0.300
1>Running UnrealBuildTool: dotnet "..\..\Engine\Binaries\DotNET\UnrealBuildTool\UnrealBuildTool.dll" IlPalazzoEditor Win64 Development -Project="C:\Users\<USER>\Desktop\IlPalazzo\IlPalazzo.uproject" -WaitMutex -FromMsBuild -architecture=x64
1>Log file: C:\Users\<USER>\AppData\Local\UnrealBuildTool\Log.txt
1>Using 'git status' to determine working set for adaptive non-unity build (C:\Users\<USER>\Desktop\IlPalazzo).
1>Creating makefile for IlPalazzoEditor (.uproject file is newer)
1>Project plugin detected, using dll at C:\Users\<USER>\Desktop\IlPalazzo\plugins\UltraLeapPlugin\Binaries\Win64\LeapC.dll
1>Unhandled exception. System.ArgumentException: Path fragment '"Content/Vectorax/Buildings/Two_Story_Villa/Geometries/\347\276\244\347\273\204_12.uasset"' contains invalid directory separators.
1>   at EpicGames.Core.FileSystemReference.CombineStrings(DirectoryReference baseDirectory, String[] fragments) in C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Core\FileSystemReference.cs:line 80
1>   at EpicGames.Core.FileReference.Combine(DirectoryReference baseDirectory, String[] fragments) in C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\Shared\EpicGames.Core\FileReference.cs:line 151
1>   at UnrealBuildTool.GitSourceFileWorkingSet.AddPath(String Path) in C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\UnrealBuildTool\System\SourceFileWorkingSet.cs:line 276
1>   at UnrealBuildTool.GitSourceFileWorkingSet.OutputDataReceived(Object Sender, DataReceivedEventArgs Args) in C:\Program Files\Epic Games\UE_5.5\Engine\Source\Programs\UnrealBuildTool\System\SourceFileWorkingSet.cs:line 242
1>   at System.Diagnostics.AsyncStreamReader.FlushMessageQueue(Boolean rethrowInNewThread)
1>--- End of stack trace from previous location ---
1>   at System.Threading.ThreadPoolWorkQueue.Dispatch()
1>   at System.Threading.PortableThreadPool.WorkerThread.WorkerThreadStart()
1>C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Microsoft\VC\v170\Microsoft.MakeFile.Targets(44,5): error MSB3073: The command ""C:\Program Files\Epic Games\UE_5.5\Engine\Build\BatchFiles\Build.bat" IlPalazzoEditor Win64 Development -Project="C:\Users\<USER>\Desktop\IlPalazzo\IlPalazzo.uproject" -WaitMutex -FromMsBuild -architecture=x64" exited with code -532462766.
1>Done building project "IlPalazzo.vcxproj" -- FAILED.
========== Build: 0 succeeded, 1 failed, 11 up-to-date, 0 skipped ==========
========== Build completed at 11:31 AM and took 02.400 seconds ==========
