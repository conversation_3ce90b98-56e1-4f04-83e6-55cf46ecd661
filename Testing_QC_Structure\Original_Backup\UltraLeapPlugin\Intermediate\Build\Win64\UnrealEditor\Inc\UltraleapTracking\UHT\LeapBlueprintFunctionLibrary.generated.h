// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "LeapBlueprintFunctionLibrary.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FLeapOptions;
struct FLeapStats;
#ifdef ULTRALEAPTRACKING_LeapBlueprintFunctionLibrary_generated_h
#error "LeapBlueprintFunctionLibrary.generated.h already included, missing '#pragma once' in LeapBlueprintFunctionLibrary.h"
#endif
#define ULTRALEAPTRACKING_LeapBlueprintFunctionLibrary_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h_22_RPC_WRAPPERS \
	DECLARE_FUNCTION(execRemoveLeapDeviceHint); \
	DECLARE_FUNCTION(execAddLeapDeviceHint); \
	DECLARE_FUNCTION(execSetLeapDeviceHints); \
	DECLARE_FUNCTION(execAngleBetweenVectors); \
	DECLARE_FUNCTION(execSetDebugRotation); \
	DECLARE_FUNCTION(execGetAppVersion); \
	DECLARE_FUNCTION(execGetAttachedLeapDevices); \
	DECLARE_FUNCTION(execSetLeapPolicy); \
	DECLARE_FUNCTION(execGetLeapStats); \
	DECLARE_FUNCTION(execGetLeapOptions); \
	DECLARE_FUNCTION(execSetLeapOptions); \
	DECLARE_FUNCTION(execSetLeapMode);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h_22_INCLASS \
private: \
	static void StaticRegisterNativesULeapBlueprintFunctionLibrary(); \
	friend struct Z_Construct_UClass_ULeapBlueprintFunctionLibrary_Statics; \
public: \
	DECLARE_CLASS(ULeapBlueprintFunctionLibrary, UBlueprintFunctionLibrary, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(ULeapBlueprintFunctionLibrary)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h_22_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API ULeapBlueprintFunctionLibrary(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(ULeapBlueprintFunctionLibrary) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ULeapBlueprintFunctionLibrary); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ULeapBlueprintFunctionLibrary); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ULeapBlueprintFunctionLibrary(ULeapBlueprintFunctionLibrary&&); \
	ULeapBlueprintFunctionLibrary(const ULeapBlueprintFunctionLibrary&); \
public: \
	NO_API virtual ~ULeapBlueprintFunctionLibrary();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h_19_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h_22_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h_22_RPC_WRAPPERS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h_22_INCLASS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h_22_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class ULeapBlueprintFunctionLibrary>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapBlueprintFunctionLibrary_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
