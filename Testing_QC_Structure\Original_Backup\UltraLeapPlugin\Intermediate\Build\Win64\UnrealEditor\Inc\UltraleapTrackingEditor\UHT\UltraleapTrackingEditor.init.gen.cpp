// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeUltraleapTrackingEditor_init() {}
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_UltraleapTrackingEditor;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_UltraleapTrackingEditor()
	{
		if (!Z_Registration_Info_UPackage__Script_UltraleapTrackingEditor.OuterSingleton)
		{
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/UltraleapTrackingEditor",
				nullptr,
				0,
				PKG_CompiledIn | 0x00000100,
				0xB13445F2,
				0xC725212F,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_UltraleapTrackingEditor.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_UltraleapTrackingEditor.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_UltraleapTrackingEditor(Z_Construct_UPackage__Script_UltraleapTrackingEditor, TEXT("/Script/UltraleapTrackingEditor"), Z_Registration_Info_UPackage__Script_UltraleapTrackingEditor, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xB13445F2, 0xC725212F));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
