// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UltraleapTrackingData.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_UltraleapTrackingData_generated_h
#error "UltraleapTrackingData.generated.h already included, missing '#pragma once' in UltraleapTrackingData.h"
#endif
#define ULTRALEAPTRACKING_UltraleapTrackingData_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_128_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeapDevice_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FLeapDevice>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_174_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeapStats_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FLeapStats>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_190_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeapOptions_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FLeapOptions>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_277_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeapBoneData_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FLeapBoneData>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_300_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeapPalmData_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FLeapPalmData>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_332_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeapDigitData_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FLeapDigitData>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_365_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeapHandData_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FLeapHandData>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_436_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FLeapFrameData_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FLeapFrameData>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_490_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FTelemetry_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FTelemetry>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h_510_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAnalytics_Statics; \
	static class UScriptStruct* StaticStruct();


template<> ULTRALEAPTRACKING_API UScriptStruct* StaticStruct<struct FAnalytics>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTrackingData_h


#define FOREACH_ENUM_EHANDTYPE(op) \
	op(LEAP_HAND_LEFT) \
	op(LEAP_HAND_RIGHT) 
#define FOREACH_ENUM_EUIINTERACTIONTYPE(op) \
	op(FAR) \
	op(NEAR) 
#define FOREACH_ENUM_ELEAPMODE(op) \
	op(LEAP_MODE_VR) \
	op(LEAP_MODE_DESKTOP) \
	op(LEAP_MODE_SCREENTOP) 
#define FOREACH_ENUM_ELEAPIMAGETYPE(op) \
	op(ELeapImageType::LEAP_IMAGE_LEFT) \
	op(ELeapImageType::LEAP_IMAGE_RIGHT) 

enum class ELeapImageType : uint8;
template<> struct TIsUEnumClass<ELeapImageType> { enum { Value = true }; };
template<> ULTRALEAPTRACKING_API UEnum* StaticEnum<ELeapImageType>();

#define FOREACH_ENUM_ELEAPTRACKINGFIDELITY(op) \
	op(LEAP_CUSTOM) \
	op(LEAP_LOW_LATENCY) \
	op(LEAP_NORMAL) \
	op(LEAP_SMOOTH) \
	op(LEAP_WIRELESS) 
#define FOREACH_ENUM_ELEAPPOLICYFLAG(op) \
	op(LEAP_POLICY_BACKGROUND_FRAMES) \
	op(LEAP_POLICY_IMAGES) \
	op(LEAP_POLICY_OPTIMIZE_HMD) \
	op(LEAP_POLICY_ALLOW_PAUSE_RESUME) \
	op(LEAP_POLICY_MAP_POINTS) 
#define FOREACH_ENUM_ELEAPSERVICELOGLEVEL(op) \
	op(LEAP_LOG_NONE) \
	op(LEAP_LOG_ERROR) \
	op(LEAP_LOG_WARNING) \
	op(LEAP_LOG_INFO) 
#define FOREACH_ENUM_ELEAPMULTIDEVICEMODE(op) \
	op(LEAP_MULTI_DEVICE_SINGULAR) \
	op(LEAP_MULTI_DEVICE_COMBINED) 
#define FOREACH_ENUM_ELEAPDEVICETYPE(op) \
	op(LEAP_DEVICE_TYPE_UNKNOWN) \
	op(LEAP_DEVICE_TYPE_PERIPHERAL) \
	op(LEAP_DEVICE_TYPE_DRAGONFLY) \
	op(LEAP_DEVICE_TYPE_NIGHTCRAWLER) \
	op(LEAP_DEVICE_TYPE_RIGEL) \
	op(LEAP_DEVICE_TYPE_SIR170) \
	op(LEAP_DEVICE_TYPE_3DI) \
	op(LEAP_DEVICE_TYPE_LEAP_MOTION_CONTROLLER_2) \
	op(LEAP_DEVICE_INVALID) 
#define FOREACH_ENUM_ELEAPDEVICECOMBINERCLASS(op) \
	op(LEAP_DEVICE_COMBINER_UNKNOWN) \
	op(LEAP_DEVICE_COMBINER_CONFIDENCE) \
	op(LEAP_DEVICE_COMBINER_ANGULAR) 
#define FOREACH_ENUM_ELEAPQUATSWIZZLEAXISB(op) \
	op(ELeapQuatSwizzleAxisB::X) \
	op(ELeapQuatSwizzleAxisB::Y) \
	op(ELeapQuatSwizzleAxisB::Z) \
	op(ELeapQuatSwizzleAxisB::W) \
	op(ELeapQuatSwizzleAxisB::MinusX) \
	op(ELeapQuatSwizzleAxisB::MinusY) \
	op(ELeapQuatSwizzleAxisB::MinusZ) \
	op(ELeapQuatSwizzleAxisB::MinusW) 

enum class ELeapQuatSwizzleAxisB : uint8;
template<> struct TIsUEnumClass<ELeapQuatSwizzleAxisB> { enum { Value = true }; };
template<> ULTRALEAPTRACKING_API UEnum* StaticEnum<ELeapQuatSwizzleAxisB>();

PRAGMA_ENABLE_DEPRECATION_WARNINGS
