{"Version": "1.2", "Data": {"Source": "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\bodystate\\module.bodystate.cpp", "ProvidedModule": "", "PCH": "c:\\users\\<USER>\\desktop\\ilpalazzo\\intermediate\\build\\win64\\x64\\ilpalazzoeditor\\development\\unrealed\\sharedpch.unrealed.project.novalfmtstr.valapi.cpp20.inclorderunreal5_3.h.pch", "Includes": ["c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\bodystate\\definitions.bodystate.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\animnode_modifybodystatemappedbones.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\animnode_modifybodystatemappedbones.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystateaniminstance.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystateenums.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateenums.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\skeleton\\bodystateskeleton.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\skeleton\\bodystatearm.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\skeleton\\bodystatebone.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatebone.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatearm.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateskeleton.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystateinputinterface.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateinputinterface.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateaniminstance.generated.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\animgraphruntime\\public\\bonecontrollers\\animnode_skeletalcontrolbase.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\animation\\inputscalebias.h", "c:\\program files\\epic games\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\inputscalebias.generated.h", "c:\\program files\\epic games\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\animgraphruntime\\uht\\animnode_skeletalcontrolbase.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\animnode_modifybodystatemappedbones.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystate.init.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateaniminstance.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatearm.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatebone.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatebonecomponent.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystatebonecomponent.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatebonecomponent.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatebplibrary.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystatebplibrary.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystatedeviceconfig.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatedeviceconfig.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatebplibrary.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatedevice.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystatedevice.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatedevice.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystatedeviceconfig.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateenums.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateestimatorcomponent.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystateestimatorcomponent.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateestimatorcomponent.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateinputinterface.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\unrealeditor\\inc\\bodystate\\uht\\bodystateskeleton.gen.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\bodystate\\permoduleinline.gen.cpp", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\core\\public\\hal\\permoduleinline.inl", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\animnode_modifybodystatemappedbones.cpp", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\engine\\public\\animation\\animinstanceproxy.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\engine\\public\\animation\\animtrace.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\engine\\public\\objecttrace.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\engine\\public\\tracefilter.h", "c:\\program files\\epic games\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\objecttrace.generated.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\engine\\public\\animation\\animattributes.h", "c:\\program files\\epic games\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\animinstanceproxy.generated.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystateaniminstance.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystateutility.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.h", "c:\\program files\\epic games\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetmathlibrary.generated.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetmathlibrary.inl", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\editor\\persona\\public\\personautils.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\editor\\classviewer\\public\\classviewermodule.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystatebonecomponent.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\ibodystate.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\inputdevice\\public\\iinputdevicemodule.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\inputdevice\\public\\iinputdevice.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystatebplibrary.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\fbodystate.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystateskeletonstorage.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystatedevice.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystatedeviceconfig.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystateestimatorcomponent.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystatehmddevice.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystatehmddevice.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\headmounteddisplay\\public\\ixrtrackingsystem.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\headmounteddisplay\\public\\headmounteddisplaytypes.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\headmounteddisplay\\public\\imotioncontroller.h", "c:\\program files\\epic games\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\headmounteddisplay\\uht\\imotioncontroller.generated.h", "c:\\program files\\epic games\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\headmounteddisplay\\uht\\headmounteddisplaytypes.generated.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\headmounteddisplay\\public\\iidentifiablexrdevice.h", "c:\\program files\\epic games\\ue_5.5\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\headmounteddisplay\\uht\\iidentifiablexrdevice.generated.h", "c:\\program files\\epic games\\ue_5.5\\engine\\source\\runtime\\headmounteddisplay\\public\\ixrinput.h", "c:\\program files\\epic games\\ue_5.5\\engine\\plugins\\runtime\\xrbase\\source\\xrbase\\public\\xrmotioncontrollerbase.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystatehmdsnapshot.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\public\\bodystatehmdsnapshot.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystateskeletonstorage.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\bodystateutility.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\fbodystate.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\fbodystateinputdevice.h", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\fbodystateinputdevice.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\skeleton\\bodystatearm.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\skeleton\\bodystatebone.cpp", "c:\\users\\<USER>\\desktop\\ilpalazzo\\plugins\\unrealplugin-main\\source\\thirdparty\\bodystate\\private\\skeleton\\bodystateskeleton.cpp"], "ImportedModules": [], "ImportedHeaderUnits": []}}