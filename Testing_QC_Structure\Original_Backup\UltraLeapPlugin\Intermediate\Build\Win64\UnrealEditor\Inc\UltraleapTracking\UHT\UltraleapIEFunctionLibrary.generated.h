// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "InteractionEngine/UltraleapIEFunctionLibrary.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UPhysicsAsset;
class UPhysicsConstraintComponent;
class USkeletalBodySetup;
#ifdef ULTRALEAPTRACKING_UltraleapIEFunctionLibrary_generated_h
#error "UltraleapIEFunctionLibrary.generated.h already included, missing '#pragma once' in UltraleapIEFunctionLibrary.h"
#endif
#define ULTRALEAPTRACKING_UltraleapIEFunctionLibrary_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h_34_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execInitPhysicsConstraint); \
	DECLARE_FUNCTION(execGetBodyName); \
	DECLARE_FUNCTION(execUpdateBoundsBodiesArray); \
	DECLARE_FUNCTION(execEnableBodyCollisionByName); \
	DECLARE_FUNCTION(execEnableBodyBoundsByName); \
	DECLARE_FUNCTION(execGetSkeletalBodySetups);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h_34_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUltraleapIEFunctionLibrary(); \
	friend struct Z_Construct_UClass_UUltraleapIEFunctionLibrary_Statics; \
public: \
	DECLARE_CLASS(UUltraleapIEFunctionLibrary, UBlueprintFunctionLibrary, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(UUltraleapIEFunctionLibrary)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h_34_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUltraleapIEFunctionLibrary(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UUltraleapIEFunctionLibrary(UUltraleapIEFunctionLibrary&&); \
	UUltraleapIEFunctionLibrary(const UUltraleapIEFunctionLibrary&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUltraleapIEFunctionLibrary); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUltraleapIEFunctionLibrary); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUltraleapIEFunctionLibrary) \
	NO_API virtual ~UUltraleapIEFunctionLibrary();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h_31_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h_34_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h_34_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h_34_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h_34_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class UUltraleapIEFunctionLibrary>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_InteractionEngine_UltraleapIEFunctionLibrary_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
