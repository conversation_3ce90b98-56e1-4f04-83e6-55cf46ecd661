// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "InteractionEngine/GraspedMovementHandler.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UPrimitiveComponent;
#ifdef ULTRALEAPTRACKING_GraspedMovementHandler_generated_h
#error "GraspedMovementHandler.generated.h already included, missing '#pragma once' in GraspedMovementHandler.h"
#endif
#define ULTRALEAPTRACKING_GraspedMovementHandler_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h_19_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execMoveTo);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h_19_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUGraspedMovementHandler(); \
	friend struct Z_Construct_UClass_UGraspedMovementHandler_Statics; \
public: \
	DECLARE_CLASS(UGraspedMovementHandler, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(UGraspedMovementHandler)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h_19_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UGraspedMovementHandler(UGraspedMovementHandler&&); \
	UGraspedMovementHandler(const UGraspedMovementHandler&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UGraspedMovementHandler); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UGraspedMovementHandler); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UGraspedMovementHandler) \
	NO_API virtual ~UGraspedMovementHandler();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h_16_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h_19_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h_19_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h_19_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h_19_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class UGraspedMovementHandler>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_GraspedMovementHandler_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
