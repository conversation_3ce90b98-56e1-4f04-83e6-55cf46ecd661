[CoreRedirects]
; So a project that references the plugin by old name, gets the new named one
; IMPORTANT, the line below isn't working in UE 4.26, so client projects need to rename the plugin name in their uprojects
;+PackageRedirects=(OldName="/LeapMotion/",NewName="/UltraleapTracking/", MatchSubstring=true)
; This is needed too, to redirect content references (as opposed to code)
; Without this skeleton references in the anim bps are messed up
+PackageRedirects=(OldName="/LeapMotion",NewName="/UltraleapTracking",MatchSubstring=true)

; Redirect class names containing LeapMotion
+ClassRedirects=(OldName="/Script/LeapMotion",NewName="/Script/UltraleapTracking",MatchSubstring=true)
; IMPORTANT the next line will cause UE 5.2 to crash if Control Rig plugins have been enabled
;+StructRedirects=(OldName="/Script/LeapMotion",NewName="/Script/UltraleapTracking",MatchSubstring=true)
+EnumRedirects=(OldName="/Script/LeapMotion",NewName="/Script/UltraleapTracking",MatchSubstring=true)
+PropertyRedirects=(OldName="/Script/LeapMotion",NewName="/Script/UltraleapTracking",MatchSubstring=true)