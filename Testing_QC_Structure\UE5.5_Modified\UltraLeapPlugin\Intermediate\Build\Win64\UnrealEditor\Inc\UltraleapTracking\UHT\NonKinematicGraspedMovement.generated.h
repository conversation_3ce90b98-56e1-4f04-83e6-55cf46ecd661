// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "InteractionEngine/NonKinematicGraspedMovement.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_NonKinematicGraspedMovement_generated_h
#error "NonKinematicGraspedMovement.generated.h already included, missing '#pragma once' in NonKinematicGraspedMovement.h"
#endif
#define ULTRALEAPTRACKING_NonKinematicGraspedMovement_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_NonKinematicGraspedMovement_h_22_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUNonKinematicGraspedMovement(); \
	friend struct Z_Construct_UClass_UNonKinematicGraspedMovement_Statics; \
public: \
	DECLARE_CLASS(UNonKinematicGraspedMovement, UGraspedMovementHandler, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(UNonKinematicGraspedMovement)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_NonKinematicGraspedMovement_h_22_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UNonKinematicGraspedMovement(UNonKinematicGraspedMovement&&); \
	UNonKinematicGraspedMovement(const UNonKinematicGraspedMovement&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UNonKinematicGraspedMovement); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UNonKinematicGraspedMovement); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UNonKinematicGraspedMovement) \
	NO_API virtual ~UNonKinematicGraspedMovement();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_NonKinematicGraspedMovement_h_19_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_NonKinematicGraspedMovement_h_22_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_NonKinematicGraspedMovement_h_22_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_NonKinematicGraspedMovement_h_22_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class UNonKinematicGraspedMovement>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_InteractionEngine_NonKinematicGraspedMovement_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
