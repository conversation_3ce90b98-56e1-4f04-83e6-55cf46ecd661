// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Multileap/MultiDeviceAlignment.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_MultiDeviceAlignment_generated_h
#error "MultiDeviceAlignment.generated.h already included, missing '#pragma once' in MultiDeviceAlignment.h"
#endif
#define ULTRALEAPTRACKING_MultiDeviceAlignment_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execUpdateTrackingDevices);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h_21_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUMultiDeviceAlignment(); \
	friend struct Z_Construct_UClass_UMultiDeviceAlignment_Statics; \
public: \
	DECLARE_CLASS(UMultiDeviceAlignment, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(UMultiDeviceAlignment)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h_21_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UMultiDeviceAlignment(UMultiDeviceAlignment&&); \
	UMultiDeviceAlignment(const UMultiDeviceAlignment&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UMultiDeviceAlignment); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UMultiDeviceAlignment); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UMultiDeviceAlignment) \
	NO_API virtual ~UMultiDeviceAlignment();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h_18_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h_21_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h_21_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h_21_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h_21_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class UMultiDeviceAlignment>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_MultiDeviceAlignment_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
