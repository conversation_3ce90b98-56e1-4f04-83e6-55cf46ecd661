// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "LeapTeleportComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UCameraComponent;
#ifdef ULTRALEAPTRACKING_LeapTeleportComponent_generated_h
#error "LeapTeleportComponent.generated.h already included, missing '#pragma once' in LeapTeleportComponent.h"
#endif
#define ULTRALEAPTRACKING_LeapTeleportComponent_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h_30_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execEndTeleportTrace); \
	DECLARE_FUNCTION(execTryTeleport); \
	DECLARE_FUNCTION(execTeleportTrace); \
	DECLARE_FUNCTION(execStartTeleportTrace); \
	DECLARE_FUNCTION(execSetTeleportCamera);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h_30_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesULeapTeleportComponent(); \
	friend struct Z_Construct_UClass_ULeapTeleportComponent_Statics; \
public: \
	DECLARE_CLASS(ULeapTeleportComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(ULeapTeleportComponent)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h_30_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ULeapTeleportComponent(ULeapTeleportComponent&&); \
	ULeapTeleportComponent(const ULeapTeleportComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ULeapTeleportComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ULeapTeleportComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ULeapTeleportComponent) \
	NO_API virtual ~ULeapTeleportComponent();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h_27_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h_30_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h_30_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h_30_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h_30_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class ULeapTeleportComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapTeleportComponent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
