// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "OneEuroFilterComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_OneEuroFilterComponent_generated_h
#error "OneEuroFilterComponent.generated.h already included, missing '#pragma once' in OneEuroFilterComponent.h"
#endif
#define ULTRALEAPTRACKING_OneEuroFilterComponent_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetDeltaCutoff); \
	DECLARE_FUNCTION(execSetCutoffSlope); \
	DECLARE_FUNCTION(execSetMinCutoff); \
	DECLARE_FUNCTION(execFilter); \
	DECLARE_FUNCTION(execInit);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h_16_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUOneEuroFilterComponent(); \
	friend struct Z_Construct_UClass_UOneEuroFilterComponent_Statics; \
public: \
	DECLARE_CLASS(UOneEuroFilterComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(UOneEuroFilterComponent)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h_16_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UOneEuroFilterComponent(UOneEuroFilterComponent&&); \
	UOneEuroFilterComponent(const UOneEuroFilterComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UOneEuroFilterComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UOneEuroFilterComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UOneEuroFilterComponent) \
	NO_API virtual ~UOneEuroFilterComponent();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h_13_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h_16_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h_16_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h_16_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h_16_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class UOneEuroFilterComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_OneEuroFilterComponent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
