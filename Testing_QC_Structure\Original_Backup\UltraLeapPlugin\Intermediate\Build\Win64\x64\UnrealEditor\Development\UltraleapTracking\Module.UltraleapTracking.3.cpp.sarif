{"version": "2.1.0", "$schema": "https://schemastore.azurewebsites.net/schemas/json/sarif-2.1.0-rtm.5.json", "runs": [{"results": [{"ruleId": "C4996", "message": {"text": "'UNiagaraComponent::SetNiagaraVariableVec3': This method will be removed in a future release.  Please update to use the FName variant Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile."}, "analysisTarget": {"uri": "file:///C:/Users/<USER>/Desktop/IlPalazzo/plugins/UnrealPlugin-main/Intermediate/Build/Win64/x64/UnrealEditor/Development/UltraleapTracking/Module.UltraleapTracking.3.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Users/<USER>/Desktop/IlPalazzo/plugins/UnrealPlugin-main/Source/UltraleapTrackingCore/Private/LeapVisualizer.cpp"}, "region": {"startLine": 74, "startColumn": 27}}}]}, {"ruleId": "C4996", "message": {"text": "'IHandTracker::GetAllKeypointStates': Deprecated in favor of the version that explicitly returns OutIsTracked Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile."}, "analysisTarget": {"uri": "file:///C:/Users/<USER>/Desktop/IlPalazzo/plugins/UnrealPlugin-main/Intermediate/Build/Win64/x64/UnrealEditor/Development/UltraleapTracking/Module.UltraleapTracking.3.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Users/<USER>/Desktop/IlPalazzo/plugins/UnrealPlugin-main/Source/UltraleapTrackingCore/Private/OpenXRToLeapWrapper.cpp"}, "region": {"startLine": 553, "startColumn": 31}}}]}, {"ruleId": "C4996", "message": {"text": "'IHandTracker::GetAllKeypointStates': Deprecated in favor of the version that explicitly returns OutIsTracked Please update your code to the new API before upgrading to the next release, otherwise your project will no longer compile."}, "analysisTarget": {"uri": "file:///C:/Users/<USER>/Desktop/IlPalazzo/plugins/UnrealPlugin-main/Intermediate/Build/Win64/x64/UnrealEditor/Development/UltraleapTracking/Module.UltraleapTracking.3.cpp"}, "locations": [{"physicalLocation": {"artifactLocation": {"uri": "file:///C:/Users/<USER>/Desktop/IlPalazzo/plugins/UnrealPlugin-main/Source/UltraleapTrackingCore/Private/OpenXRToLeapWrapper.cpp"}, "region": {"startLine": 554, "startColumn": 32}}}]}], "tool": {"driver": {"name": "MSVC", "shortDescription": {"text": "Microsoft Visual C++ Compiler Warnings/Errors"}, "informationUri": "https://docs.microsoft.com/cpp/error-messages/compiler-errors-1/c-cpp-build-errors"}}}]}