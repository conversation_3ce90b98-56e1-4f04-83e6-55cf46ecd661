{"RemapUnityFiles": {"Module.UltraleapTracking.1.cpp.obj": ["GrabClassifierComponent.gen.cpp.obj", "GraspedMovementHandler.gen.cpp.obj", "JointOcclusionActor.gen.cpp.obj", "LeapBlueprintFunctionLibrary.gen.cpp.obj", "LeapComponent.gen.cpp.obj", "LeapHandActor.gen.cpp.obj", "LeapSubsystem.gen.cpp.obj", "LeapTeleportComponent.gen.cpp.obj"], "Module.UltraleapTracking.2.cpp.obj": ["LeapTrackingSettings.gen.cpp.obj", "LeapVisualizer.gen.cpp.obj", "LeapWidgetInteractionComponent.gen.cpp.obj", "MultiDeviceAlignment.gen.cpp.obj", "NonKinematicGraspedMovement.gen.cpp.obj", "OneEuroFilterComponent.gen.cpp.obj", "OpenXRToLeapWrapper.gen.cpp.obj", "TickInEditorStaticMeshComponent.gen.cpp.obj", "TrackingDeviceBaseActor.gen.cpp.obj", "UltraleapEditorNotifyComponent2.gen.cpp.obj", "UltraleapIEFunctionLibrary.gen.cpp.obj", "UltraleapInputListenerComponent.gen.cpp.obj", "UltraleapTickInEditorBaseActor.gen.cpp.obj", "UltraleapTracking.init.gen.cpp.obj", "UltraleapTrackingData.gen.cpp.obj", "PerModuleInline.gen.cpp.obj", "FUltraleapDevice.cpp.obj", "FUltraleapTrackingInputDevice.cpp.obj"], "Module.UltraleapTracking.3.cpp.obj": ["FUltraleapTrackingPlugin.cpp.obj", "GrabClassifierComponent.cpp.obj", "GraspedMovementHandler.cpp.obj", "NonKinematicGraspedMovement.cpp.obj", "UltraleapIEFunctionLibrary.cpp.obj", "LeapAsync.cpp.obj", "LeapBlueprintFunctionLibrary.cpp.obj", "LeapComponent.cpp.obj", "LeapDeviceWrapper.cpp.obj", "LeapHandActor.cpp.obj", "LeapImage.cpp.obj", "LeapLiveLink.cpp.obj", "LeapSubsystem.cpp.obj", "LeapTeleportComponent.cpp.obj", "LeapTrackingSettings.cpp.obj", "LeapUtility.cpp.obj", "LeapVisualizer.cpp.obj", "LeapWidgetInteractionComponent.cpp.obj", "LeapWrapper.cpp.obj", "DeviceCombiner.cpp.obj", "FKabschSolver.cpp.obj", "FUltraleapCombinedDevice.cpp.obj", "FUltraleapCombinedDeviceAngular.cpp.obj", "FUltraleapCombinedDeviceConfidence.cpp.obj", "JointOcclusionActor.cpp.obj", "MultiDeviceAlignment.cpp.obj", "OneEuroFilterComponent.cpp.obj", "OpenXRToLeapWrapper.cpp.obj", "TickInEditorStaticMeshComponent.cpp.obj", "TrackingDeviceBaseActor.cpp.obj", "UltraleapEditorNotifyComponent.cpp.obj", "UltraleapInputListenerComponent.cpp.obj", "UltraleapTickInEditorBaseActor.cpp.obj", "UltraleapTrackingData.cpp.obj"]}}