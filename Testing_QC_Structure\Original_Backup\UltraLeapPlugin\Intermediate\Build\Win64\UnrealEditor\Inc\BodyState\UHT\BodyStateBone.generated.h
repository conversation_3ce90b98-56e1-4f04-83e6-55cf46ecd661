// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Skeleton/BodyStateBone.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
struct FBodyStateBoneMeta;
#ifdef BODYSTATE_BodyStateBone_generated_h
#error "BodyStateBone.generated.h already included, missing '#pragma once' in BodyStateBone.h"
#endif
#define BODYSTATE_BodyStateBone_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_29_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBodyStateBoneMeta_Statics; \
	static class UScriptStruct* StaticStruct();


template<> BODYSTATE_API UScriptStruct* StaticStruct<struct FBodyStateBoneMeta>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_68_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBodyStateBoneData_Statics; \
	static class UScriptStruct* StaticStruct();


template<> BODYSTATE_API UScriptStruct* StaticStruct<struct FBodyStateBoneData>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_114_RPC_WRAPPERS \
	DECLARE_FUNCTION(execIsTracked); \
	DECLARE_FUNCTION(execChangeBasis); \
	DECLARE_FUNCTION(execShiftBone); \
	DECLARE_FUNCTION(execSetEnabled); \
	DECLARE_FUNCTION(execEnabled); \
	DECLARE_FUNCTION(execUniqueMeta); \
	DECLARE_FUNCTION(execSetScale); \
	DECLARE_FUNCTION(execTransform); \
	DECLARE_FUNCTION(execScale); \
	DECLARE_FUNCTION(execSetOrientation); \
	DECLARE_FUNCTION(execOrientation); \
	DECLARE_FUNCTION(execSetPosition); \
	DECLARE_FUNCTION(execPosition);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_114_INCLASS \
private: \
	static void StaticRegisterNativesUBodyStateBone(); \
	friend struct Z_Construct_UClass_UBodyStateBone_Statics; \
public: \
	DECLARE_CLASS(UBodyStateBone, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/BodyState"), NO_API) \
	DECLARE_SERIALIZER(UBodyStateBone)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_114_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UBodyStateBone(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UBodyStateBone) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UBodyStateBone); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UBodyStateBone); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UBodyStateBone(UBodyStateBone&&); \
	UBodyStateBone(const UBodyStateBone&); \
public: \
	NO_API virtual ~UBodyStateBone();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_111_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_114_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_114_RPC_WRAPPERS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_114_INCLASS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h_114_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BODYSTATE_API UClass* StaticClass<class UBodyStateBone>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateBone_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
