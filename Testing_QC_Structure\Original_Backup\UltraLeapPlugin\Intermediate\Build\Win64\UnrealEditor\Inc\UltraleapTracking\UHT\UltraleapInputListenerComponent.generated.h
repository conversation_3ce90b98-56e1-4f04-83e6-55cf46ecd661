// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UltraleapInputListenerComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_UltraleapInputListenerComponent_generated_h
#error "UltraleapInputListenerComponent.generated.h already included, missing '#pragma once' in UltraleapInputListenerComponent.h"
#endif
#define ULTRALEAPTRACKING_UltraleapInputListenerComponent_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_16_DELEGATE \
ULTRALEAPTRACKING_API void FOnInputActionUL_DelegateWrapper(const FScriptDelegate& OnInputActionUL);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execStopListeningForInputAction); \
	DECLARE_FUNCTION(execListenForInputAction);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_24_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUltraleapInputListenerComponent(); \
	friend struct Z_Construct_UClass_UUltraleapInputListenerComponent_Statics; \
public: \
	DECLARE_CLASS(UUltraleapInputListenerComponent, USceneComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(UUltraleapInputListenerComponent)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_24_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UUltraleapInputListenerComponent(UUltraleapInputListenerComponent&&); \
	UUltraleapInputListenerComponent(const UUltraleapInputListenerComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUltraleapInputListenerComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUltraleapInputListenerComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(UUltraleapInputListenerComponent) \
	NO_API virtual ~UUltraleapInputListenerComponent();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_21_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_24_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_24_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_24_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h_24_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class UUltraleapInputListenerComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapInputListenerComponent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
