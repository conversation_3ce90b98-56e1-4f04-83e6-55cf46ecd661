// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Multileap/JointOcclusionActor.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_JointOcclusionActor_generated_h
#error "JointOcclusionActor.generated.h already included, missing '#pragma once' in JointOcclusionActor.h"
#endif
#define ULTRALEAPTRACKING_JointOcclusionActor_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h_31_RPC_WRAPPERS_NO_PURE_DECLS \
	DECLARE_FUNCTION(execSetupColours); \
	DECLARE_FUNCTION(execGetJointOcclusionConfidences);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h_31_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAJointOcclusionActor(); \
	friend struct Z_Construct_UClass_AJointOcclusionActor_Statics; \
public: \
	DECLARE_CLASS(AJointOcclusionActor, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(AJointOcclusionActor)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h_31_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AJointOcclusionActor(AJointOcclusionActor&&); \
	AJointOcclusionActor(const AJointOcclusionActor&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AJointOcclusionActor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AJointOcclusionActor); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AJointOcclusionActor) \
	NO_API virtual ~AJointOcclusionActor();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h_28_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h_31_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h_31_RPC_WRAPPERS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h_31_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h_31_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class AJointOcclusionActor>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_Multileap_JointOcclusionActor_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
