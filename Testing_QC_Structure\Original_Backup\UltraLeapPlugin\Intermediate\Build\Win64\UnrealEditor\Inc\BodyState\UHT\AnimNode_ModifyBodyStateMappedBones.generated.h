// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AnimNode_ModifyBodyStateMappedBones.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef BODYSTATE_AnimNode_ModifyBodyStateMappedBones_generated_h
#error "AnimNode_ModifyBodyStateMappedBones.generated.h already included, missing '#pragma once' in AnimNode_ModifyBodyStateMappedBones.h"
#endif
#define BODYSTATE_AnimNode_ModifyBodyStateMappedBones_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_AnimNode_ModifyBodyStateMappedBones_h_33_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FAnimNode_ModifyBodyStateMappedBones_Statics; \
	static class UScriptStruct* StaticStruct(); \
	typedef FAnimNode_SkeletalControlBase Super;


template<> BODYSTATE_API UScriptStruct* StaticStruct<struct FAnimNode_ModifyBodyStateMappedBones>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_AnimNode_ModifyBodyStateMappedBones_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
