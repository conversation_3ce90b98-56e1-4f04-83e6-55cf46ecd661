// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "TrackingDeviceBaseActor.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_TrackingDeviceBaseActor_generated_h
#error "TrackingDeviceBaseActor.generated.h already included, missing '#pragma once' in TrackingDeviceBaseActor.h"
#endif
#define ULTRALEAPTRACKING_TrackingDeviceBaseActor_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_TrackingDeviceBaseActor_h_19_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesATrackingDeviceBaseActor(); \
	friend struct Z_Construct_UClass_ATrackingDeviceBaseActor_Statics; \
public: \
	DECLARE_CLASS(ATrackingDeviceBaseActor, AUltraleapTickInEditorBaseActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(ATrackingDeviceBaseActor)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_TrackingDeviceBaseActor_h_19_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ATrackingDeviceBaseActor(ATrackingDeviceBaseActor&&); \
	ATrackingDeviceBaseActor(const ATrackingDeviceBaseActor&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ATrackingDeviceBaseActor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ATrackingDeviceBaseActor); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ATrackingDeviceBaseActor) \
	NO_API virtual ~ATrackingDeviceBaseActor();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_TrackingDeviceBaseActor_h_16_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_TrackingDeviceBaseActor_h_19_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_TrackingDeviceBaseActor_h_19_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_TrackingDeviceBaseActor_h_19_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class ATrackingDeviceBaseActor>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Private_TrackingDeviceBaseActor_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
