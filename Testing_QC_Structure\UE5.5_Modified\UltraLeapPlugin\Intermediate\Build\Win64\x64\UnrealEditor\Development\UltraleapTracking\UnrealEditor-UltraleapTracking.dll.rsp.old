/MANIFEST:EMBED
/MANIFESTINPUT:"..\Build\Windows\Resources\Default-Win64.manifest"
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.38.33130\lib\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\um\x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
/NATVIS:"..\Plugins\FX\Niagara\Intermediate\Build\Win64\x64\UnrealEditor\Development\Niagara\Niagara.natvis"
/NATVIS:"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\RenderCore\RenderCore.natvis"
"C:\Users\<USER>\Desktop\IlPalazzo\Intermediate\Build\Win64\x64\IlPalazzoEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.NoValFmtStr.ValApi.Cpp20.InclOrderUnreal5_3.h.obj"
"C:\Users\<USER>\Desktop\IlPalazzo\Plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\UltraleapTracking\Module.UltraleapTracking.1.cpp.obj"
"C:\Users\<USER>\Desktop\IlPalazzo\Plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\UltraleapTracking\Module.UltraleapTracking.2.cpp.obj"
"C:\Users\<USER>\Desktop\IlPalazzo\Plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\UltraleapTracking\Module.UltraleapTracking.3.cpp.obj"
"C:\Users\<USER>\Desktop\IlPalazzo\Plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\UltraleapTracking\Default.rc2.res"
"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Source\ThirdParty\LeapSDK\Lib\Win64\LeapC.lib"
"..\Plugins\Runtime\XRBase\Intermediate\Build\Win64\x64\UnrealEditor\Development\XRBase\UnrealEditor-XRBase.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Json\UnrealEditor-Json.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\JsonUtilities\UnrealEditor-JsonUtilities.lib"
"..\Plugins\FX\Niagara\Intermediate\Build\Win64\x64\UnrealEditor\Development\NiagaraCore\UnrealEditor-NiagaraCore.lib"
"..\Plugins\FX\Niagara\Intermediate\Build\Win64\x64\UnrealEditor\Development\Niagara\UnrealEditor-Niagara.lib"
"..\Plugins\FX\Niagara\Intermediate\Build\Win64\x64\UnrealEditor\Development\NiagaraShader\UnrealEditor-NiagaraShader.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\NavigationSystem\UnrealEditor-NavigationSystem.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\ApplicationCore\UnrealEditor-ApplicationCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Engine\UnrealEditor-Engine.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Core\UnrealEditor-Core.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\CoreUObject\UnrealEditor-CoreUObject.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\InputCore\UnrealEditor-InputCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\InputDevice\UnrealEditor-InputDevice.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Slate\UnrealEditor-Slate.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\SlateCore\UnrealEditor-SlateCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\HeadMountedDisplay\UnrealEditor-HeadMountedDisplay.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\RHI\UnrealEditor-RHI.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\RenderCore\UnrealEditor-RenderCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Projects\UnrealEditor-Projects.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\LiveLinkInterface\UnrealEditor-LiveLinkInterface.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\LiveLinkMessageBusFramework\UnrealEditor-LiveLinkMessageBusFramework.lib"
"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\BodyState\UnrealEditor-BodyState.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\PhysicsCore\UnrealEditor-PhysicsCore.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UMG\UnrealEditor-UMG.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
"uiautomationcore.lib"
"DXGI.lib"
/OUT:"C:\Users\<USER>\Desktop\IlPalazzo\Plugins\UnrealPlugin-main\Binaries\Win64\UnrealEditor-UltraleapTracking.dll"
/PDB:"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Binaries\Win64\UnrealEditor-UltraleapTracking.pdb"
/ignore:4078