// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "AnimGraphNode_ModifyBodyStateMappedBones.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKINGEDITOR_AnimGraphNode_ModifyBodyStateMappedBones_generated_h
#error "AnimGraphNode_ModifyBodyStateMappedBones.generated.h already included, missing '#pragma once' in AnimGraphNode_ModifyBodyStateMappedBones.h"
#endif
#define ULTRALEAPTRACKINGEDITOR_AnimGraphNode_ModifyBodyStateMappedBones_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingEditor_Public_AnimGraphNode_ModifyBodyStateMappedBones_h_24_INCLASS \
private: \
	static void StaticRegisterNativesUAnimGraphNode_ModifyBodyStateMappedBones(); \
	friend struct Z_Construct_UClass_UAnimGraphNode_ModifyBodyStateMappedBones_Statics; \
public: \
	DECLARE_CLASS(UAnimGraphNode_ModifyBodyStateMappedBones, UAnimGraphNode_SkeletalControlBase, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/UltraleapTrackingEditor"), ULTRALEAPTRACKINGEDITOR_API) \
	DECLARE_SERIALIZER(UAnimGraphNode_ModifyBodyStateMappedBones)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingEditor_Public_AnimGraphNode_ModifyBodyStateMappedBones_h_24_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	ULTRALEAPTRACKINGEDITOR_API UAnimGraphNode_ModifyBodyStateMappedBones(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UAnimGraphNode_ModifyBodyStateMappedBones) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(ULTRALEAPTRACKINGEDITOR_API, UAnimGraphNode_ModifyBodyStateMappedBones); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UAnimGraphNode_ModifyBodyStateMappedBones); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UAnimGraphNode_ModifyBodyStateMappedBones(UAnimGraphNode_ModifyBodyStateMappedBones&&); \
	UAnimGraphNode_ModifyBodyStateMappedBones(const UAnimGraphNode_ModifyBodyStateMappedBones&); \
public: \
	ULTRALEAPTRACKINGEDITOR_API virtual ~UAnimGraphNode_ModifyBodyStateMappedBones();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingEditor_Public_AnimGraphNode_ModifyBodyStateMappedBones_h_21_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingEditor_Public_AnimGraphNode_ModifyBodyStateMappedBones_h_24_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingEditor_Public_AnimGraphNode_ModifyBodyStateMappedBones_h_24_INCLASS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingEditor_Public_AnimGraphNode_ModifyBodyStateMappedBones_h_24_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKINGEDITOR_API UClass* StaticClass<class UAnimGraphNode_ModifyBodyStateMappedBones>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingEditor_Public_AnimGraphNode_ModifyBodyStateMappedBones_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
