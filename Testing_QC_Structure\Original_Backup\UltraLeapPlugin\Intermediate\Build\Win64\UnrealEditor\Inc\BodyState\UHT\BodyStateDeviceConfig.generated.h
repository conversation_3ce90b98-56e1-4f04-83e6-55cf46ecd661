// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "BodyStateDeviceConfig.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef BODYSTATE_BodyStateDeviceConfig_generated_h
#error "BodyStateDeviceConfig.generated.h already included, missing '#pragma once' in BodyStateDeviceConfig.h"
#endif
#define BODYSTATE_BodyStateDeviceConfig_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateDeviceConfig_h_39_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBodyStateDeviceConfig_Statics; \
	static class UScriptStruct* StaticStruct();


template<> BODYSTATE_API UScriptStruct* StaticStruct<struct FBodyStateDeviceConfig>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateDeviceConfig_h


#define FOREACH_ENUM_EBODYSTATEDEVICEINPUTTYPE(op) \
	op(INERTIAL_INPUT_TYPE) \
	op(HMD_MOUNTED_INPUT_TYPE) \
	op(EXTERNAL_REFERENCE_INPUT_TYPE) \
	op(MIXED_INPUT_TYPE) 
PRAGMA_ENABLE_DEPRECATION_WARNINGS
