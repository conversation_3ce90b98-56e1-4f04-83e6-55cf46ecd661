// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UltraleapTickInEditorBaseActor.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_UltraleapTickInEditorBaseActor_generated_h
#error "UltraleapTickInEditorBaseActor.generated.h already included, missing '#pragma once' in UltraleapTickInEditorBaseActor.h"
#endif
#define ULTRALEAPTRACKING_UltraleapTickInEditorBaseActor_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h_17_CALLBACK_WRAPPERS
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h_17_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesAUltraleapTickInEditorBaseActor(); \
	friend struct Z_Construct_UClass_AUltraleapTickInEditorBaseActor_Statics; \
public: \
	DECLARE_CLASS(AUltraleapTickInEditorBaseActor, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(AUltraleapTickInEditorBaseActor)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h_17_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	AUltraleapTickInEditorBaseActor(AUltraleapTickInEditorBaseActor&&); \
	AUltraleapTickInEditorBaseActor(const AUltraleapTickInEditorBaseActor&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, AUltraleapTickInEditorBaseActor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(AUltraleapTickInEditorBaseActor); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(AUltraleapTickInEditorBaseActor) \
	NO_API virtual ~AUltraleapTickInEditorBaseActor();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h_14_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h_17_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h_17_CALLBACK_WRAPPERS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h_17_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h_17_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class AUltraleapTickInEditorBaseActor>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapTickInEditorBaseActor_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
