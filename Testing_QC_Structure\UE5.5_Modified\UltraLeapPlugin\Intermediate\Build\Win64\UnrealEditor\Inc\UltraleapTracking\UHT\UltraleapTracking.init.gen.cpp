// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeUltraleapTracking_init() {}
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_GrabClassifierGrabStateChanged__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapDeviceSignature__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapEventSignature__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapFrameSignature__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapGrab__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapGrabAction__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapHandFaceCamera__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapHandSignature__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapImageEventSignature__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapPolicySignature__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapRayComponentVisible__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapRelease__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapTrackingModeSignature__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_LeapVisibilityBoolSignature__DelegateSignature();
	ULTRALEAPTRACKING_API UFunction* Z_Construct_UDelegateFunction_UltraleapTracking_OnInputActionUL__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_UltraleapTracking;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_UltraleapTracking()
	{
		if (!Z_Registration_Info_UPackage__Script_UltraleapTracking.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_GrabClassifierGrabStateChanged__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapDeviceSignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapEventSignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapFrameSignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapGrab__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapGrabAction__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapHandFaceCamera__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapHandSignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapImageEventSignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapPolicySignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapRayComponentVisible__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapRelease__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapTrackingModeSignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_LeapVisibilityBoolSignature__DelegateSignature,
				(UObject* (*)())Z_Construct_UDelegateFunction_UltraleapTracking_OnInputActionUL__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/UltraleapTracking",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0x0BC78ADE,
				0x7E3E3939,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_UltraleapTracking.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_UltraleapTracking.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_UltraleapTracking(Z_Construct_UPackage__Script_UltraleapTracking, TEXT("/Script/UltraleapTracking"), Z_Registration_Info_UPackage__Script_UltraleapTracking, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0x0BC78ADE, 0x7E3E3939));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
