// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "LeapHandActor.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_LeapHandActor_generated_h
#error "LeapHandActor.generated.h already included, missing '#pragma once' in LeapHandActor.h"
#endif
#define ULTRALEAPTRACKING_LeapHandActor_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapHandActor_h_23_DELEGATE \
ULTRALEAPTRACKING_API void FLeapHandFaceCamera_DelegateWrapper(const FMulticastScriptDelegate& LeapHandFaceCamera, bool bIsFacingCamera);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapHandActor_h_33_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesALeapHandActor(); \
	friend struct Z_Construct_UClass_ALeapHandActor_Statics; \
public: \
	DECLARE_CLASS(ALeapHandActor, AActor, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(ALeapHandActor)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapHandActor_h_33_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ALeapHandActor(ALeapHandActor&&); \
	ALeapHandActor(const ALeapHandActor&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ALeapHandActor); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ALeapHandActor); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ALeapHandActor) \
	NO_API virtual ~ALeapHandActor();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapHandActor_h_30_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapHandActor_h_33_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapHandActor_h_33_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapHandActor_h_33_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class ALeapHandActor>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapHandActor_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
