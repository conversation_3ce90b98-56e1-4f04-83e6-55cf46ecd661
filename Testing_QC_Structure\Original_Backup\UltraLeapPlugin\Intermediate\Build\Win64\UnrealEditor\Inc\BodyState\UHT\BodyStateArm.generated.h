// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Skeleton/BodyStateArm.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UBodyStateFinger;
#ifdef BODYSTATE_BodyStateArm_generated_h
#error "BodyStateArm.generated.h already included, missing '#pragma once' in BodyStateArm.h"
#endif
#define BODYSTATE_BodyStateArm_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_31_INCLASS \
private: \
	static void StaticRegisterNativesUBodyStateFinger(); \
	friend struct Z_Construct_UClass_UBodyStateFinger_Statics; \
public: \
	DECLARE_CLASS(UBodyStateFinger, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/BodyState"), NO_API) \
	DECLARE_SERIALIZER(UBodyStateFinger)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_31_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UBodyStateFinger(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UBodyStateFinger) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UBodyStateFinger); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UBodyStateFinger); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UBodyStateFinger(UBodyStateFinger&&); \
	UBodyStateFinger(const UBodyStateFinger&); \
public: \
	NO_API virtual ~UBodyStateFinger();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_28_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_31_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_31_INCLASS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_31_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BODYSTATE_API UClass* StaticClass<class UBodyStateFinger>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_54_RPC_WRAPPERS \
	DECLARE_FUNCTION(execPinkyFinger); \
	DECLARE_FUNCTION(execRingFinger); \
	DECLARE_FUNCTION(execMiddleFinger); \
	DECLARE_FUNCTION(execIndexFinger); \
	DECLARE_FUNCTION(execThumbFinger);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_54_INCLASS \
private: \
	static void StaticRegisterNativesUBodyStateHand(); \
	friend struct Z_Construct_UClass_UBodyStateHand_Statics; \
public: \
	DECLARE_CLASS(UBodyStateHand, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/BodyState"), NO_API) \
	DECLARE_SERIALIZER(UBodyStateHand)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_54_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UBodyStateHand(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UBodyStateHand) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UBodyStateHand); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UBodyStateHand); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UBodyStateHand(UBodyStateHand&&); \
	UBodyStateHand(const UBodyStateHand&); \
public: \
	NO_API virtual ~UBodyStateHand();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_51_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_54_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_54_RPC_WRAPPERS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_54_INCLASS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_54_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BODYSTATE_API UClass* StaticClass<class UBodyStateHand>();

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_85_INCLASS \
private: \
	static void StaticRegisterNativesUBodyStateArm(); \
	friend struct Z_Construct_UClass_UBodyStateArm_Statics; \
public: \
	DECLARE_CLASS(UBodyStateArm, UObject, COMPILED_IN_FLAGS(0), CASTCLASS_None, TEXT("/Script/BodyState"), NO_API) \
	DECLARE_SERIALIZER(UBodyStateArm)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_85_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UBodyStateArm(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UBodyStateArm) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UBodyStateArm); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UBodyStateArm); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UBodyStateArm(UBodyStateArm&&); \
	UBodyStateArm(const UBodyStateArm&); \
public: \
	NO_API virtual ~UBodyStateArm();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_82_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_85_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_85_INCLASS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h_85_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BODYSTATE_API UClass* StaticClass<class UBodyStateArm>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_Skeleton_BodyStateArm_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
