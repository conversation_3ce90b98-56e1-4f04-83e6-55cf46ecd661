// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "BodyStateBoneComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef BODYSTATE_BodyStateBoneComponent_generated_h
#error "BodyStateBoneComponent.generated.h already included, missing '#pragma once' in BodyStateBoneComponent.h"
#endif
#define BODYSTATE_BodyStateBoneComponent_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateBoneComponent_h_34_INCLASS \
private: \
	static void StaticRegisterNativesUBodyStateBoneComponent(); \
	friend struct Z_Construct_UClass_UBodyStateBoneComponent_Statics; \
public: \
	DECLARE_CLASS(UBodyStateBoneComponent, USceneComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/BodyState"), NO_API) \
	DECLARE_SERIALIZER(UBodyStateBoneComponent)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateBoneComponent_h_34_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UBodyStateBoneComponent(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UBodyStateBoneComponent) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UBodyStateBoneComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UBodyStateBoneComponent); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UBodyStateBoneComponent(UBodyStateBoneComponent&&); \
	UBodyStateBoneComponent(const UBodyStateBoneComponent&); \
public: \
	NO_API virtual ~UBodyStateBoneComponent();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateBoneComponent_h_31_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateBoneComponent_h_34_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateBoneComponent_h_34_INCLASS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateBoneComponent_h_34_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BODYSTATE_API UClass* StaticClass<class UBodyStateBoneComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateBoneComponent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
