// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
PRAGMA_DISABLE_DEPRECATION_WARNINGS
void EmptyLinkFunctionForGeneratedCodeBodyState_init() {}
	BODYSTATE_API UFunction* Z_Construct_UDelegateFunction_BodyState_BodyStateSkeletonSignature__DelegateSignature();
	static FPackageRegistrationInfo Z_Registration_Info_UPackage__Script_BodyState;
	FORCENOINLINE UPackage* Z_Construct_UPackage__Script_BodyState()
	{
		if (!Z_Registration_Info_UPackage__Script_BodyState.OuterSingleton)
		{
			static UObject* (*const SingletonFuncArray[])() = {
				(UObject* (*)())Z_Construct_UDelegateFunction_BodyState_BodyStateSkeletonSignature__DelegateSignature,
			};
			static const UECodeGen_Private::FPackageParams PackageParams = {
				"/Script/BodyState",
				SingletonFuncArray,
				UE_ARRAY_COUNT(SingletonFuncArray),
				PKG_CompiledIn | 0x00000000,
				0xA42330D3,
				0x4889647C,
				METADATA_PARAMS(0, nullptr)
			};
			UECodeGen_Private::ConstructUPackage(Z_Registration_Info_UPackage__Script_BodyState.OuterSingleton, PackageParams);
		}
		return Z_Registration_Info_UPackage__Script_BodyState.OuterSingleton;
	}
	static FRegisterCompiledInInfo Z_CompiledInDeferPackage_UPackage__Script_BodyState(Z_Construct_UPackage__Script_BodyState, TEXT("/Script/BodyState"), Z_Registration_Info_UPackage__Script_BodyState, CONSTRUCT_RELOAD_VERSION_INFO(FPackageReloadVersionInfo, 0xA42330D3, 0x4889647C));
PRAGMA_ENABLE_DEPRECATION_WARNINGS
