// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "UltraleapEditorNotifyComponent2.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_UltraleapEditorNotifyComponent2_generated_h
#error "UltraleapEditorNotifyComponent2.generated.h already included, missing '#pragma once' in UltraleapEditorNotifyComponent2.h"
#endif
#define ULTRALEAPTRACKING_UltraleapEditorNotifyComponent2_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h_20_CALLBACK_WRAPPERS
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h_20_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesUUltraleapEditorNotifyComponent2(); \
	friend struct Z_Construct_UClass_UUltraleapEditorNotifyComponent2_Statics; \
public: \
	DECLARE_CLASS(UUltraleapEditorNotifyComponent2, UStaticMeshComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(UUltraleapEditorNotifyComponent2)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h_20_ENHANCED_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UUltraleapEditorNotifyComponent2(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UUltraleapEditorNotifyComponent2(UUltraleapEditorNotifyComponent2&&); \
	UUltraleapEditorNotifyComponent2(const UUltraleapEditorNotifyComponent2&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UUltraleapEditorNotifyComponent2); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UUltraleapEditorNotifyComponent2); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UUltraleapEditorNotifyComponent2) \
	NO_API virtual ~UUltraleapEditorNotifyComponent2();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h_17_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h_20_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h_20_CALLBACK_WRAPPERS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h_20_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h_20_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class UUltraleapEditorNotifyComponent2>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_UltraleapEditorNotifyComponent2_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
