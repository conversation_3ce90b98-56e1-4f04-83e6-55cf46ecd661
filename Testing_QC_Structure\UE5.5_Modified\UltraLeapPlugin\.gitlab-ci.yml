variables:
  # use the strategy set in the project CI settings GIT_STRATEGY: none
  GIT_SUBMODULE_STRATEGY: none
  ErrorActionPreference: stop

  PROJECT_NAME: "UltraleapTracking"
  PROJECT_PATH: "$CI_PROJECT_DIR"   
  
  ENGINE_PATH_UE5_1: "C:/Program Files/Epic Games/UE_5.1"
  ENGINE_UAT_PATH_UE5_1: "$ENGINE_PATH_UE5_1/Engine/Build/BatchFiles/RunUAT.bat"

  ENGINE_PATH_UE5_3: "C:/Program Files/Epic Games/UE_5.3"
  ENGINE_UAT_PATH_UE5_3: "$ENGINE_PATH_UE5_3/Engine/Build/BatchFiles/RunUAT.bat"
  
  ENGINE_PATH_UE5_4: "C:/Program Files/Epic Games/UE_5.4"
  ENGINE_UAT_PATH_UE5_4: "$ENGINE_PATH_UE5_4/Engine/Build/BatchFiles/RunUAT.bat"

  ENGINE_PATH_UE5_5: "C:/Program Files/Epic Games/UE_5.5"
  ENGINE_UAT_PATH_UE5_5: "$ENGINE_PATH_UE5_5/Engine/Build/BatchFiles/RunUAT.bat"

  # this holds the unreal version
  UNREAL_ENGINE_BUILD_VERSION_FILE: "$ENGINE_PATH_UE5_1/Engine/Build/Build.version"
  UNREAL_ENGINE_BUILD_VERSION_FILE_UE5_4: "$ENGINE_PATH_UE5_4/Engine/Build/Build.version"
  UNREAL_ENGINE_BUILD_VERSION_FILE_UE5_3: "$ENGINE_PATH_UE5_3/Engine/Build/Build.version"
  UNREAL_ENGINE_BUILD_VERSION_FILE_UE5_5: "$ENGINE_PATH_UE5_5/Engine/Build/Build.version"
  
  VERSIONTRUNC: "Unknown"

stages:
  - documentation
  - build
  - deploy

documentation::generate-tracking-documentation:
  stage: documentation
  tags:
    - unreal
    - windows  
  timeout: 30m

  script:
     # Generate XML documentation using Doxygen
    - echo "Generate docs"

    # this uses the presets saved in Doxyfile and outputs to DoxyOutput/xml
    - doxygen

  after_script:
    - $DOXY_LOCATION = "$PROJECT_PATH/DoxyOutput"
    - echo "$DOXY_LOCATION"
    
    - mv "$DOXY_LOCATION/*" "$CI_PROJECT_DIR"
  artifacts:
    name: "Artifacts"
    paths:
      - "xml"

    when: always
    expire_in: never
  needs: []

build:build-project:
  stage: build
  tags:
    - unreal
    - windows  
  when: always
  retry: 2
  timeout: 1h
  allow_failure: false

  script:   

    # Get Unreal version and changeset from the project. from json
    - $UNREAL_VERSION_CONTENT_UE5_1 = Get-Content -Raw -Path $UNREAL_ENGINE_BUILD_VERSION_FILE | ConvertFrom-Json
    - $UNREAL_VERSION_UE5_1 = $UNREAL_VERSION_CONTENT_UE5_1.MajorVersion.ToString() + "." + $UNREAL_VERSION_CONTENT_UE5_1.MinorVersion.ToString()  + "." + $UNREAL_VERSION_CONTENT_UE5_1.PatchVersion.ToString()

    - $UNREAL_VERSION_CONTENT_UE5_3 = Get-Content -Raw -Path $UNREAL_ENGINE_BUILD_VERSION_FILE_UE5_3 | ConvertFrom-Json
    - $UNREAL_VERSION_UE5_3 = $UNREAL_VERSION_CONTENT_UE5_3.MajorVersion.ToString() + "." + $UNREAL_VERSION_CONTENT_UE5_3.MinorVersion.ToString()  + "." + $UNREAL_VERSION_CONTENT_UE5_3.PatchVersion.ToString()
    
    - $UNREAL_VERSION_CONTENT_UE5_4 = Get-Content -Raw -Path $UNREAL_ENGINE_BUILD_VERSION_FILE_UE5_4 | ConvertFrom-Json
    - $UNREAL_VERSION_UE5_4 = $UNREAL_VERSION_CONTENT_UE5_4.MajorVersion.ToString() + "." + $UNREAL_VERSION_CONTENT_UE5_4.MinorVersion.ToString()  + "." + $UNREAL_VERSION_CONTENT_UE5_4.PatchVersion.ToString()

    - $UNREAL_VERSION_CONTENT_UE5_5 = Get-Content -Raw -Path $UNREAL_ENGINE_BUILD_VERSION_FILE_UE5_5 | ConvertFrom-Json
    - $UNREAL_VERSION_UE5_5 = $UNREAL_VERSION_CONTENT_UE5_5.MajorVersion.ToString() + "." + $UNREAL_VERSION_CONTENT_UE5_5.MinorVersion.ToString()  + "." + $UNREAL_VERSION_CONTENT_UE5_5.PatchVersion.ToString()

    - echo "$UNREAL_VERSION_UE5_1"
    - echo "$UNREAL_VERSION_UE5_3"
    - echo "$UNREAL_VERSION_UE5_4"
    - echo "$UNREAL_VERSION_UE5_5"
    
    - echo "$ENGINE_PATH_UE5_1"
    - echo "$ENGINE_PATH_UE5_3"
    - echo "$ENGINE_PATH_UE5_4"
    - echo "$ENGINE_PATH_UE5_5"
    - echo "$PROJECT_PATH"
    - echo "$PROJECT_PATH/$PROJECT_NAME.uplugin"
    - echo "$ENGINE_UAT_PATH_UE5_1"
    - echo "$ENGINE_UAT_PATH_UE5_3"
    - echo "$ENGINE_UAT_PATH_UE5_4"
    - echo "$ENGINE_UAT_PATH_UE5_5"
    - echo "Working dir"
    - pwd
   
    # Build the project.
    - echo "==============================================="
    - echo "Build with UAT"
    - echo "==============================================="

    ################################## 5.1
    - $UE5_PACKAGE_PATH_1 = "$PROJECT_PATH/$PROJECT_NAME" + "_ue5_1"
    - echo "$UE5_PACKAGE_PATH_1"
    
    # Piped to a log file, remove at the end of this line if you want to see the output during build
    - $process = Start-Process -Wait -PassThru -NoNewWindow -FilePath "$ENGINE_UAT_PATH_UE5_1" -ArgumentList "BuildPlugin -Plugin=$PROJECT_PATH/$PROJECT_NAME.uplugin -Package=$UE5_PACKAGE_PATH_1 -CreateSubFolder -VS2019 -TargetPlatforms=Win64+Mac -Rocket > ./$PROJECT_NAME.log" 
    - echo $process.ExitCode
    - if (-not ($process.ExitCode -eq 0)) { exit $process.ExitCode }
   
    - echo "Unreal 5.1 build complete."

    ################################## 5.3
    - $UE5_PACKAGE_PATH_3 = "$PROJECT_PATH/$PROJECT_NAME" + "_ue5_3"
    - echo "$UE5_PACKAGE_PATH_3"

    - $process = Start-Process -Wait -PassThru -NoNewWindow -FilePath "$ENGINE_UAT_PATH_UE5_3" -ArgumentList "BuildPlugin -Plugin=$PROJECT_PATH/$PROJECT_NAME.uplugin -Package=$UE5_PACKAGE_PATH_3 -CreateSubFolder -VS2022 -TargetPlatforms=Win64+Mac -Rocket  >> ./$PROJECT_NAME.log" 
    - echo $process.ExitCode
    - if (-not ($process.ExitCode -eq 0)) { exit $process.ExitCode }
    
    - echo "Unreal 5.3 build complete."


    ################################## 5.4
    - $UE5_PACKAGE_PATH_4 = "$PROJECT_PATH/$PROJECT_NAME" + "_ue5_4"
    - echo "$UE5_PACKAGE_PATH_4"

    - $process = Start-Process -Wait -PassThru -NoNewWindow -FilePath "$ENGINE_UAT_PATH_UE5_4" -ArgumentList "BuildPlugin -Plugin=$PROJECT_PATH/$PROJECT_NAME.uplugin -Package=$UE5_PACKAGE_PATH_4 -CreateSubFolder -VS2022 -TargetPlatforms=Win64+Mac -Rocket  >> ./$PROJECT_NAME.log" 
    - echo $process.ExitCode
    - if (-not ($process.ExitCode -eq 0)) { exit $process.ExitCode }
    
    - echo "Unreal 5.4 build complete."

    ################################## 5.5
    - $UE5_PACKAGE_PATH_5 = "$PROJECT_PATH/$PROJECT_NAME" + "_ue5_5"
    - echo "$UE5_PACKAGE_PATH_5"

    - $process = Start-Process -Wait -PassThru -NoNewWindow -FilePath "$ENGINE_UAT_PATH_UE5_5" -ArgumentList "BuildPlugin -Plugin=$PROJECT_PATH/$PROJECT_NAME.uplugin -Package=$UE5_PACKAGE_PATH_5 -CreateSubFolder -VS2022 -TargetPlatforms=Win64+Mac -Rocket  >> ./$PROJECT_NAME.log"
    - echo $process.ExitCode
    - if (-not ($process.ExitCode -eq 0)) { exit $process.ExitCode }

    - echo "Unreal 5.5 build complete."


  after_script:
     # Get the project/app version from the uplugin file
     # this holds the plugin version
    - $UNREAL_PLUGIN_FILE = "$PROJECT_PATH/$PROJECT_NAME.uplugin"
    - $BUILD_LOCATION_UE5_1 = "$PROJECT_PATH/$PROJECT_NAME" + "_ue5_1"
    - $BUILD_LOCATION_UE5_3 = "$PROJECT_PATH/$PROJECT_NAME" + "_ue5_3"
    - $BUILD_LOCATION_UE5_4 =  "$PROJECT_PATH/$PROJECT_NAME" + "_ue5_4"
    - $BUILD_LOCATION_UE5_5 =  "$PROJECT_PATH/$PROJECT_NAME" + "_ue5_5"

    - $VERSION =  Get-Content $UNREAL_PLUGIN_FILE | Where-Object {$_ -like '*VersionName*'}
    - $VERSIONTRUNC = $VERSION.Replace('VersionName','')
    - $VERSIONTRUNC = $VERSIONTRUNC.Replace(':','')
    - $VERSIONTRUNC = $VERSIONTRUNC.Replace(' ','')
    - $VERSIONTRUNC = $VERSIONTRUNC.Replace('",','')
    - $VERSIONTRUNC = $VERSIONTRUNC.Replace('"','')
    - $VERSIONTRUNC = $VERSIONTRUNC.Replace('	','')
    

    - echo "$VERSIONTRUNC"
    
    - $BUILD_FULL_VERSION = $VERSIONTRUNC
    - $BUILD_PATH_UE5_1 = "./build/$PROJECT_NAME" + "_ue5_1" +"-$BUILD_FULL_VERSION/"
    - $BUILD_PATH_UE5_3 = "./build/$PROJECT_NAME" + "_ue5_3" +"-$BUILD_FULL_VERSION/"
    - $BUILD_PATH_UE5_4 = "./build/$PROJECT_NAME" + "_ue5_4" +"-$BUILD_FULL_VERSION/"
    - $BUILD_PATH_UE5_5 = "./build/$PROJECT_NAME" + "_ue5_5" +"-$BUILD_FULL_VERSION/"

    - mkdir "$BUILD_PATH_UE5_1"
    - mkdir "$BUILD_PATH_UE5_3"
    - mkdir "$BUILD_PATH_UE5_4"
    - mkdir "$BUILD_PATH_UE5_5"

    - echo "$BUILD_PATH_UE5_1"
    - echo "$BUILD_PATH_UE5_3"
    - echo "$BUILD_PATH_UE5_4"
    - echo "$BUILD_PATH_UE5_5"

    - echo "$BUILD_LOCATION_UE5_1"
    - echo "$BUILD_LOCATION_UE5_3"
    - echo "$BUILD_LOCATION_UE5_4"
    - echo "$BUILD_LOCATION_UE5_5"


    - mv "$BUILD_LOCATION_UE5_1/*" "$BUILD_PATH_UE5_1"
    - mv "$BUILD_LOCATION_UE5_3/*" "$BUILD_PATH_UE5_3"
    - mv "$BUILD_LOCATION_UE5_4/*" "$BUILD_PATH_UE5_4"
    - mv "$BUILD_LOCATION_UE5_5/*" "$BUILD_PATH_UE5_5"

     # pdbs
    - Remove-Item "$BUILD_PATH_UE5_1/Binaries/Win64/*.pdb"
    - Remove-Item "$BUILD_PATH_UE5_3/Binaries/Win64/*.pdb"
    - Remove-Item "$BUILD_PATH_UE5_4/Binaries/Win64/*.pdb"
    - Remove-Item "$BUILD_PATH_UE5_5/Binaries/Win64/*.pdb"
   
    # Copy the packaged plugin for modifications for the Unreal Marketplace submission
    - echo "copying and modifying for the marketplace"
    - $BUILD_MARKET_PLACE_PATH_UE5_1 = "./build/MarketPlace_5_1/$PROJECT_NAME"
    - $BUILD_MARKET_PLACE_PATH_UE5_3 = "./build/MarketPlace_5_3/$PROJECT_NAME"
    - $BUILD_MARKET_PLACE_PATH_UE5_4 = "./build/MarketPlace_5_4/$PROJECT_NAME"
    - $BUILD_MARKET_PLACE_PATH_UE5_5 = "./build/MarketPlace_5_5/$PROJECT_NAME"

    - echo "$BUILD_MARKET_PLACE_PATH_UE5_1"
    - echo "$BUILD_MARKET_PLACE_PATH_UE5_3"
    - echo "$BUILD_MARKET_PLACE_PATH_UE5_4"
    - echo "$BUILD_MARKET_PLACE_PATH_UE5_5"


    - mkdir "$BUILD_MARKET_PLACE_PATH_UE5_1"
    - mkdir "$BUILD_MARKET_PLACE_PATH_UE5_3"
    - mkdir "$BUILD_MARKET_PLACE_PATH_UE5_4"
    - mkdir "$BUILD_MARKET_PLACE_PATH_UE5_5"

    # UE5.1 contents
    - Copy-Item "$BUILD_PATH_UE5_1*" -Destination "$BUILD_MARKET_PLACE_PATH_UE5_1" -Recurse
    # Copy the FilterPlugin.ini
    - echo "$PROJECT_PATH/Config/FilterPlugin.ini"
    - Copy-Item "$PROJECT_PATH/Config/FilterPlugin.ini" -Destination "$BUILD_MARKET_PLACE_PATH_UE5_1/Config"
    # Clean the binaries folder
    # pdbs
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_1/Binaries/Win64/*.pdb"
    # modules
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_1/Binaries/Win64/UnrealEditor.modules"
    # dlls
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_1/Binaries/Win64/UnrealEditor-BodyState.dll"
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_1/Binaries/Win64/UnrealEditor-UltraleapTracking.dll"
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_1/Binaries/Win64/UnrealEditor-UltraleapTrackingEditor.dll"
    # intermediate folder
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_1/Intermediate" -Recurse

    # zip it
    - Compress-Archive -Path "$BUILD_MARKET_PLACE_PATH_UE5_1" -DestinationPath "$BUILD_MARKET_PLACE_PATH_UE5_1.zip"
 



    ## UE5.3 contents
    - Copy-Item "$BUILD_PATH_UE5_3*" -Destination "$BUILD_MARKET_PLACE_PATH_UE5_3" -Recurse
    ## Copy the FilterPlugin.ini
    - echo "$PROJECT_PATH/Config/FilterPlugin.ini"
    - Copy-Item "$PROJECT_PATH/Config/FilterPlugin.ini" -Destination "$BUILD_MARKET_PLACE_PATH_UE5_3/Config"
    ## Clean the binaries folder
    ## pdbs
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_3/Binaries/Win64/*.pdb"
    ## modules
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_3/Binaries/Win64/UnrealEditor.modules"
    ## dlls
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_3/Binaries/Win64/UnrealEditor-BodyState.dll"
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_3/Binaries/Win64/UnrealEditor-UltraleapTracking.dll"
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_3/Binaries/Win64/UnrealEditor-UltraleapTrackingEditor.dll"
    ## intermediate folder
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_3/Intermediate" -Recurse

    ## zip it
    - Compress-Archive -Path "$BUILD_MARKET_PLACE_PATH_UE5_3" -DestinationPath "$BUILD_MARKET_PLACE_PATH_UE5_3.zip"
 



    ## UE5.4 contents
    - Copy-Item "$BUILD_PATH_UE5_4*" -Destination "$BUILD_MARKET_PLACE_PATH_UE5_4" -Recurse
    ## Copy the FilterPlugin.ini
    - echo "$PROJECT_PATH/Config/FilterPlugin.ini"
    - Copy-Item "$PROJECT_PATH/Config/FilterPlugin.ini" -Destination "$BUILD_MARKET_PLACE_PATH_UE5_4/Config"
    ## Clean the binaries folder
    ## pdbs
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_4/Binaries/Win64/*.pdb"
    ## modules
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_4/Binaries/Win64/UnrealEditor.modules"
    ## dlls
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_4/Binaries/Win64/UnrealEditor-BodyState.dll"
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_4/Binaries/Win64/UnrealEditor-UltraleapTracking.dll"
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_4/Binaries/Win64/UnrealEditor-UltraleapTrackingEditor.dll"
    ## intermediate folder
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_4/Intermediate" -Recurse

    ## zip it
    - Compress-Archive -Path "$BUILD_MARKET_PLACE_PATH_UE5_4" -DestinationPath "$BUILD_MARKET_PLACE_PATH_UE5_4.zip"



    ## UE5.5 contents
    - Copy-Item "$BUILD_PATH_UE5_5*" -Destination "$BUILD_MARKET_PLACE_PATH_UE5_5" -Recurse
    ## Copy the FilterPlugin.ini
    - echo "$PROJECT_PATH/Config/FilterPlugin.ini"
    - Copy-Item "$PROJECT_PATH/Config/FilterPlugin.ini" -Destination "$BUILD_MARKET_PLACE_PATH_UE5_5/Config"
    ## Clean the binaries folder
    ## pdbs
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_5/Binaries/Win64/*.pdb"
    ## modules
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_5/Binaries/Win64/UnrealEditor.modules"
    ## dlls
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_5/Binaries/Win64/UnrealEditor-BodyState.dll"
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_5/Binaries/Win64/UnrealEditor-UltraleapTracking.dll"
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_5/Binaries/Win64/UnrealEditor-UltraleapTrackingEditor.dll"
    ## intermediate folder
    - Remove-Item "$BUILD_MARKET_PLACE_PATH_UE5_5/Intermediate" -Recurse

    ## zip it
    - Compress-Archive -Path "$BUILD_MARKET_PLACE_PATH_UE5_5" -DestinationPath "$BUILD_MARKET_PLACE_PATH_UE5_5.zip"


    # repare github zips with bins file
    - $GITHUB_PATH_UE5_1 = "./github/$PROJECT_NAME" + "_ue5_1"
    - $GITHUB_PATH_UE5_3 = "./github/$PROJECT_NAME" + "_ue5_3"
    - $GITHUB_PATH_UE5_4 = "./github/$PROJECT_NAME" + "_ue5_4"
    - $GITHUB_PATH_UE5_5 = "./github/$PROJECT_NAME" + "_ue5_5"

    - mkdir "$GITHUB_PATH_UE5_1"
    - mkdir "$GITHUB_PATH_UE5_3"
    - mkdir "$GITHUB_PATH_UE5_4"
    - mkdir "$GITHUB_PATH_UE5_5"

    - Compress-Archive -Path "$BUILD_PATH_UE5_1" -DestinationPath "${GITHUB_PATH_UE5_1}/${PROJECT_NAME}_ue5_1.zip"
    - Compress-Archive -Path "$BUILD_PATH_UE5_3" -DestinationPath "${GITHUB_PATH_UE5_3}/${PROJECT_NAME}_ue5_3.zip"
    - Compress-Archive -Path "$BUILD_PATH_UE5_4" -DestinationPath "${GITHUB_PATH_UE5_4}/${PROJECT_NAME}_ue5_4.zip"
    - Compress-Archive -Path "$BUILD_PATH_UE5_5" -DestinationPath "${GITHUB_PATH_UE5_5}/${PROJECT_NAME}_ue5_5.zip"


  artifacts:

    name: "$env:CI_PROJECT_NAME-$env:CI_COMMIT_REF_SLUG-$env:CI_JOB_ID-$env:CI_COMMIT_SHORT_SHA"
    paths:
    - "./$PROJECT_NAME.log"
    - "./build/MarketPlace_5_1/${PROJECT_NAME}.zip"
    - "./build/MarketPlace_5_3/${PROJECT_NAME}.zip"
    - "./build/MarketPlace_5_4/${PROJECT_NAME}.zip"
    - "./build/MarketPlace_5_5/${PROJECT_NAME}.zip"

    # these artifiacts are for github
    - "./github/${PROJECT_NAME}_ue5_1/${PROJECT_NAME}_ue5_1.zip"
    - "./github/${PROJECT_NAME}_ue5_3/${PROJECT_NAME}_ue5_3.zip"
    - "./github/${PROJECT_NAME}_ue5_4/${PROJECT_NAME}_ue5_4.zip"
    - "./github/${PROJECT_NAME}_ue5_5/${PROJECT_NAME}_ue5_5.zip"
    
    when: always
    expire_in: 2 weeks
  needs: []


# Upload build artifacts to an S3 bucket for public access.
# Created by Luke Driffield in the IT team.
# AWS account access managed by the IT team.
# Please raise an IT Support ticket for support; if required.
deploy:upload-to-aws-s3:
  
  tags:
    - docker

  image:
    # Uses official Amazon AWS CLI image from Docker Hub
    name: amazon/aws-cli
    # Change entrypoint to 'nothing' to allow us to use shell commands
    entrypoint: [""]


  # Run after the build stage
  stage: deploy
  # Run manually; can be changed if desired
  when: manual

  

  variables:
    # Basename for output ZIP used for upload
    ZIP_NAME: UltraleapTracking
    # CI_BUILDS_DIR provides basepath from CI/CD variables
    BUILD_DIR: ${CI_PROJECT_DIR}/build/
    # Basename used for output directories
    BUILD_DIR_NAME: MarketPlace
    # AWS Bucket details
    AWS_BUCKET_NAME: ultraleap-public-files
    AWS_BUCKET_REGION: eu-west-2
    AWS_UPLOAD_TXT: "${CI_PROJECT_DIR}/s3-download-urls.txt"
  
  script:

    - yum install -y zip
    - touch "${AWS_UPLOAD_TXT}"

    - echo "===== Started S3 Upload $(date +'%A %d %b %T %Z %Y') =====" > "${AWS_UPLOAD_TXT}"

    - |

      for VERSION in "5_1" "5_3" "5_4" "5_5"; do

         export ZIP_OUTPUT_NAME="${ZIP_NAME}_${VERSION}_$(cat /dev/urandom | base64 | tr -dc '0-9a-zA-Z' | head -c12).zip";
         mv "${BUILD_DIR}/MarketPlace_${VERSION}/${ZIP_NAME}.zip" "${ZIP_OUTPUT_NAME}"
         aws s3 cp "${ZIP_OUTPUT_NAME}" "s3://$AWS_BUCKET_NAME/${ZIP_OUTPUT_NAME}" --acl public-read
         echo "${ZIP_OUTPUT_NAME} => https://${AWS_BUCKET_NAME}.s3.${AWS_BUCKET_REGION}.amazonaws.com/${ZIP_OUTPUT_NAME}"
         echo "${ZIP_OUTPUT_NAME} => https://${AWS_BUCKET_NAME}.s3.${AWS_BUCKET_REGION}.amazonaws.com/${ZIP_OUTPUT_NAME}" >> "${AWS_UPLOAD_TXT}"

      done



  after_script:
    - echo "===== Finished S3 Upload $(date +'%A %d %b %T %Z %Y') =====" >> "${AWS_UPLOAD_TXT}"
  
  artifacts:
    paths: [ "s3-download-urls.txt" ]
    expire_in: 1 month
    expose_as: File Download URL List
    name: s3-download-urls
    when: always
  
  # Setup job dependencies
  needs:
    - job: build:build-project
      artifacts: true
