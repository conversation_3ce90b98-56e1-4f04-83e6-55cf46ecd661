// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "LeapWidgetInteractionComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
#ifdef ULTRALEAPTRACKING_LeapWidgetInteractionComponent_generated_h
#error "LeapWidgetInteractionComponent.generated.h already included, missing '#pragma once' in LeapWidgetInteractionComponent.h"
#endif
#define ULTRALEAPTRACKING_LeapWidgetInteractionComponent_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapWidgetInteractionComponent_h_24_DELEGATE \
ULTRALEAPTRACKING_API void FLeapRayComponentVisible_DelegateWrapper(const FMulticastScriptDelegate& LeapRayComponentVisible, bool Visible);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapWidgetInteractionComponent_h_34_INCLASS_NO_PURE_DECLS \
private: \
	static void StaticRegisterNativesULeapWidgetInteractionComponent(); \
	friend struct Z_Construct_UClass_ULeapWidgetInteractionComponent_Statics; \
public: \
	DECLARE_CLASS(ULeapWidgetInteractionComponent, UWidgetInteractionComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/UltraleapTracking"), NO_API) \
	DECLARE_SERIALIZER(ULeapWidgetInteractionComponent)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapWidgetInteractionComponent_h_34_ENHANCED_CONSTRUCTORS \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	ULeapWidgetInteractionComponent(ULeapWidgetInteractionComponent&&); \
	ULeapWidgetInteractionComponent(const ULeapWidgetInteractionComponent&); \
public: \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, ULeapWidgetInteractionComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(ULeapWidgetInteractionComponent); \
	DEFINE_DEFAULT_CONSTRUCTOR_CALL(ULeapWidgetInteractionComponent)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapWidgetInteractionComponent_h_31_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapWidgetInteractionComponent_h_34_GENERATED_BODY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapWidgetInteractionComponent_h_34_INCLASS_NO_PURE_DECLS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapWidgetInteractionComponent_h_34_ENHANCED_CONSTRUCTORS \
private: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> ULTRALEAPTRACKING_API UClass* StaticClass<class ULeapWidgetInteractionComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_UltraleapTrackingCore_Public_LeapWidgetInteractionComponent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
