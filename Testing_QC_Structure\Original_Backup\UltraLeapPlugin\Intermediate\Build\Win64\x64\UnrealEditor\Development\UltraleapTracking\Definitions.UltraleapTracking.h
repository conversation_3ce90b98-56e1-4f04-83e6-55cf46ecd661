// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for UltraleapTracking
#pragma once
#include "C:/Users/<USER>/Desktop/IlPalazzo/Intermediate/Build/Win64/x64/IlPalazzoEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.NoValFmtStr.ValApi.Cpp20.InclOrderUnreal5_3.h"
#undef ULTRALEAPTRACKING_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 0
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_PROJECT_NAME IlPalazzo
#define UE_TARGET_NAME IlPalazzoEditor
#define UE_MODULE_NAME "UltraleapTracking"
#define UE_PLUGIN_NAME "UltraleapTracking"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define XRBASE_API DLLIMPORT
#define AUGMENTEDREALITY_API DLLIMPORT
#define MRMESH_API DLLIMPORT
#define NIAGARACORE_API DLLIMPORT
#define VECTORVM_SUPPORTS_EXPERIMENTAL 1
#define VECTORVM_SUPPORTS_LEGACY 1
#define VECTORVM_SUPPORTS_SERIALIZATION 0
#define VECTORVM_DEBUG_PRINTF 0
#define VECTORVM_API DLLIMPORT
#define NIAGARA_API DLLIMPORT
#define NIAGARASHADER_API DLLIMPORT
#define NIAGARAVERTEXFACTORIES_API DLLIMPORT
#define ULTRALEAPTRACKING_API DLLEXPORT
#define INPUTDEVICE_API DLLIMPORT
#define LIVELINKINTERFACE_API DLLIMPORT
#define LIVELINKMESSAGEBUSFRAMEWORK_API DLLIMPORT
#define BODYSTATE_API DLLIMPORT
#define WITH_LAUNCHERCHECK 0
#define LAUNCH_API DLLIMPORT
#define SESSIONSERVICES_API DLLIMPORT
