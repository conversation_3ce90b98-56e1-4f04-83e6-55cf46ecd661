/MANIFEST:EMBED
/MANIFESTINPUT:"..\Build\Windows\Resources\Default-Win64.manifest"
/NOLOGO
/DEBUG:FULL
/errorReport:prompt
/MACHINE:x64
/SUBSYSTEM:WINDOWS
/FIXED:No
/NXCOMPAT
/STACK:12000000
/DELAY:UNLOAD
/DLL
/PDBALTPATH:%_PDB%
/d2:-ExtendedWarningInfo
/OPT:NOREF
/OPT:NOICF
/INCREMENTAL:NO
/ignore:4199
/ignore:4099
/ALTERNATENAME:__imp___std_init_once_begin_initialize=__imp_InitOnceBeginInitialize
/ALTERNATENAME:__imp___std_init_once_complete=__imp_InitOnceComplete
/DELAYLOAD:"d3d12.dll"
/DELAYLOAD:"DBGHELP.DLL"
/LIBPATH:"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.38.33130\lib\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\ucrt\x64"
/LIBPATH:"C:\Program Files (x86)\Windows Kits\10\lib\10.0.22621.0\um\x64"
/NODEFAULTLIB:"LIBCMT"
/NODEFAULTLIB:"LIBCPMT"
/NODEFAULTLIB:"LIBCMTD"
/NODEFAULTLIB:"LIBCPMTD"
/NODEFAULTLIB:"MSVCRTD"
/NODEFAULTLIB:"MSVCPRTD"
/NODEFAULTLIB:"LIBC"
/NODEFAULTLIB:"LIBCP"
/NODEFAULTLIB:"LIBCD"
/NODEFAULTLIB:"LIBCPD"
/FUNCTIONPADMIN:6
/NOIMPLIB
"C:\Users\<USER>\Desktop\IlPalazzo\Intermediate\Build\Win64\x64\IlPalazzoEditor\Development\UnrealEd\SharedPCH.UnrealEd.Project.NoValFmtStr.ValApi.Cpp20.InclOrderUnreal5_3.h.obj"
"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\UltraleapTrackingEditor\Module.UltraleapTrackingEditor.cpp.obj"
"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\UltraleapTrackingEditor\Default.rc2.res"
"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Source\ThirdParty\LeapSDK\Lib\Win64\LeapC.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\UnrealEd\UnrealEditor-UnrealEd.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\BlueprintGraph\UnrealEditor-BlueprintGraph.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\AnimGraph\UnrealEditor-AnimGraph.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\AnimGraphRuntime\UnrealEditor-AnimGraphRuntime.lib"
"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\BodyState\UnrealEditor-BodyState.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Engine\UnrealEditor-Engine.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Core\UnrealEditor-Core.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\CoreUObject\UnrealEditor-CoreUObject.lib"
"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Intermediate\Build\Win64\x64\UnrealEditor\Development\UltraleapTracking\UnrealEditor-UltraleapTracking.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\PropertyEditor\UnrealEditor-PropertyEditor.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\Slate\UnrealEditor-Slate.lib"
"..\Intermediate\Build\Win64\x64\UnrealEditor\Development\SlateCore\UnrealEditor-SlateCore.lib"
"delayimp.lib"
"wininet.lib"
"rpcrt4.lib"
"ws2_32.lib"
"dbghelp.lib"
"comctl32.lib"
"Winmm.lib"
"kernel32.lib"
"user32.lib"
"gdi32.lib"
"winspool.lib"
"comdlg32.lib"
"advapi32.lib"
"shell32.lib"
"ole32.lib"
"oleaut32.lib"
"uuid.lib"
"odbc32.lib"
"odbccp32.lib"
"netapi32.lib"
"iphlpapi.lib"
"setupapi.lib"
"synchronization.lib"
"dwmapi.lib"
"imm32.lib"
/OUT:"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Binaries\Win64\UnrealEditor-UltraleapTrackingEditor.dll"
/PDB:"C:\Users\<USER>\Desktop\IlPalazzo\plugins\UnrealPlugin-main\Binaries\Win64\UnrealEditor-UltraleapTrackingEditor.pdb"
/ignore:4078