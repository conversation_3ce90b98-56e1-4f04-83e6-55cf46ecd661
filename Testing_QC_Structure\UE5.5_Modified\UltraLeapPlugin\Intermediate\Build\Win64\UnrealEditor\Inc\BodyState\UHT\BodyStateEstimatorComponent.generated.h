// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "BodyStateEstimatorComponent.h"
#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS
class UBodyStateSkeleton;
#ifdef BODYSTATE_BodyStateEstimatorComponent_generated_h
#error "BodyStateEstimatorComponent.generated.h already included, missing '#pragma once' in BodyStateEstimatorComponent.h"
#endif
#define BODYSTATE_BodyStateEstimatorComponent_generated_h

#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateEstimatorComponent_h_28_DELEGATE \
BODYSTATE_API void FBodyStateSkeletonSignature_DelegateWrapper(const FMulticastScriptDelegate& BodyStateSkeletonSignature, UBodyStateSkeleton* Skeleton);


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateEstimatorComponent_h_37_INCLASS \
private: \
	static void StaticRegisterNativesUBodyStateEstimatorComponent(); \
	friend struct Z_Construct_UClass_UBodyStateEstimatorComponent_Statics; \
public: \
	DECLARE_CLASS(UBodyStateEstimatorComponent, UActorComponent, COMPILED_IN_FLAGS(0 | CLASS_Config), CASTCLASS_None, TEXT("/Script/BodyState"), NO_API) \
	DECLARE_SERIALIZER(UBodyStateEstimatorComponent)


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateEstimatorComponent_h_37_STANDARD_CONSTRUCTORS \
	/** Standard constructor, called after all reflected properties have been initialized */ \
	NO_API UBodyStateEstimatorComponent(const FObjectInitializer& ObjectInitializer = FObjectInitializer::Get()); \
	DEFINE_DEFAULT_OBJECT_INITIALIZER_CONSTRUCTOR_CALL(UBodyStateEstimatorComponent) \
	DECLARE_VTABLE_PTR_HELPER_CTOR(NO_API, UBodyStateEstimatorComponent); \
	DEFINE_VTABLE_PTR_HELPER_CTOR_CALLER(UBodyStateEstimatorComponent); \
private: \
	/** Private move- and copy-constructors, should never be used */ \
	UBodyStateEstimatorComponent(UBodyStateEstimatorComponent&&); \
	UBodyStateEstimatorComponent(const UBodyStateEstimatorComponent&); \
public: \
	NO_API virtual ~UBodyStateEstimatorComponent();


#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateEstimatorComponent_h_34_PROLOG
#define FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateEstimatorComponent_h_37_GENERATED_BODY_LEGACY \
PRAGMA_DISABLE_DEPRECATION_WARNINGS \
public: \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateEstimatorComponent_h_37_INCLASS \
	FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateEstimatorComponent_h_37_STANDARD_CONSTRUCTORS \
public: \
PRAGMA_ENABLE_DEPRECATION_WARNINGS


template<> BODYSTATE_API UClass* StaticClass<class UBodyStateEstimatorComponent>();

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Users_harol_Desktop_IlPalazzo_plugins_UnrealPlugin_main_Source_ThirdParty_BodyState_Public_BodyStateEstimatorComponent_h


PRAGMA_ENABLE_DEPRECATION_WARNINGS
