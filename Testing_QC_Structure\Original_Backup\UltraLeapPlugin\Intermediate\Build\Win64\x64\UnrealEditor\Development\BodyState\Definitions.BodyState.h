// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for BodyState
#pragma once
#include "C:/Users/<USER>/Desktop/IlPalazzo/Intermediate/Build/Win64/x64/IlPalazzoEditor/Development/UnrealEd/SharedDefinitions.UnrealEd.Project.NoValFmtStr.ValApi.Cpp20.InclOrderUnreal5_3.h"
#undef BODYSTATE_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 0
#define UE_VALIDATE_INTERNAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 1
#define UE_PROJECT_NAME IlPalazzo
#define UE_TARGET_NAME IlPalazzoEditor
#define UE_MODULE_NAME "BodyState"
#define UE_PLUGIN_NAME "UltraleapTracking"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define ANIMGRAPHRUNTIME_API DLLIMPORT
#define INPUTDEVICE_API DLLIMPORT
#define XRBASE_API DLLIMPORT
#define AUGMENTEDREALITY_API DLLIMPORT
#define MRMESH_API DLLIMPORT
#define BODYSTATE_API DLLEXPORT
